/* Garden Planner Web Application - Custom Styles */

/* Material 3 Design System Extensions */
.rounded-material {
    border-radius: 12px;
}

.shadow-material-1 {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-material-2 {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-material-3 {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-material-4 {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-material-5 {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Sage color palette for Material 3 */
.bg-sage-50 { background-color: #f8faf8; }
.bg-sage-100 { background-color: #f1f5f1; }
.bg-sage-200 { background-color: #e4ebe4; }
.bg-sage-300 { background-color: #d1ddd1; }
.bg-sage-400 { background-color: #b8c9b8; }
.bg-sage-500 { background-color: #9fb59f; }
.bg-sage-600 { background-color: #87a187; }
.bg-sage-700 { background-color: #6f8d6f; }
.bg-sage-800 { background-color: #5a7a5a; }
.bg-sage-900 { background-color: #4a6b4a; }

.text-sage-50 { color: #f8faf8; }
.text-sage-100 { color: #f1f5f1; }
.text-sage-200 { color: #e4ebe4; }
.text-sage-300 { color: #d1ddd1; }
.text-sage-400 { color: #b8c9b8; }
.text-sage-500 { color: #9fb59f; }
.text-sage-600 { color: #87a187; }
.text-sage-700 { color: #6f8d6f; }
.text-sage-800 { color: #5a7a5a; }
.text-sage-900 { color: #4a6b4a; }

.border-sage-200 { border-color: #e4ebe4; }
.border-sage-300 { border-color: #d1ddd1; }
.border-sage-600 { border-color: #87a187; }
.border-sage-700 { border-color: #6f8d6f; }

/* Drawing tools specific styles */
.tool-button {
    @apply px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
}

.tool-button.active {
    @apply bg-primary-600 text-white border-primary-600;
}

.canvas-container {
    @apply relative border-2 border-gray-300 rounded-lg overflow-hidden;
    min-height: 400px;
}

/* Button styles */
.btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200;
}

.btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 text-white font-medium py-2 px-4 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200;
}

.btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200;
}

.btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200;
}

/* Form styles */
.form-input {
    @apply w-full px-3 py-2 border border-sage-300 rounded-material focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-sage-700 dark:border-sage-600 dark:text-sage-100;
}

.form-label {
    @apply block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2;
}

/* Navigation styles */
.nav-link {
    @apply text-white hover:text-sage-200 px-3 py-2 rounded-md text-sm font-medium transition-colors;
}

.nav-link.active {
    @apply bg-sage-700 text-white;
}

/* Card styles */
.card {
    @apply bg-white dark:bg-sage-800 rounded-material shadow-material-2 border border-sage-200 dark:border-sage-700;
}

.card-header {
    @apply px-6 py-4 border-b border-sage-200 dark:border-sage-600;
}

.card-body {
    @apply px-6 py-4;
}

/* Notification styles */
.notification-item {
    @apply px-4 py-3 hover:bg-sage-50 dark:hover:bg-sage-700 border-b border-sage-100 dark:border-sage-600 cursor-pointer transition-colors;
}

/* Statistics dashboard */
.stat-card {
    @apply bg-white dark:bg-sage-800 rounded-lg shadow-md p-4 text-center;
}

.stat-value {
    @apply text-2xl font-bold;
}

.stat-label {
    @apply text-sm text-sage-600 dark:text-sage-400;
}

/* Responsive design */
@media (max-width: 768px) {
    .canvas-container {
        min-height: 300px;
    }
    
    .tool-button {
        @apply px-2 py-1 text-xs;
    }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
    .canvas-container {
        @apply border-sage-600;
    }
}

/* Animation utilities */
.transition-all {
    transition: all 0.2s ease-in-out;
}

.transition-colors {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* Grid and drawing specific styles */
.grid-toggle-btn.active {
    background-color: #3b82f6 !important;
    color: white !important;
    border: 2px solid #3b82f6 !important;
}

/* Wishlist styles */
.wishlist-item {
    @apply flex items-center justify-between p-4 bg-sage-50 dark:bg-sage-700 rounded-material border border-sage-200 dark:border-sage-600;
}

/* Property visualization */
.property-shape {
    fill: rgba(59, 130, 246, 0.2);
    stroke: rgba(59, 130, 246, 0.8);
    stroke-width: 2;
}

.growing-area-shape {
    fill: rgba(34, 197, 94, 0.3);
    stroke: rgba(34, 197, 94, 0.8);
    stroke-width: 2;
}
