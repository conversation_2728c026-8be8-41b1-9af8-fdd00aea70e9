#!/usr/bin/env python3
"""
Simple test script to verify all main routes are working
"""
import requests
import sys

BASE_URL = "http://127.0.0.1:8080"

def test_route(path, expected_status=200, description=""):
    """Test a single route"""
    try:
        response = requests.get(f"{BASE_URL}{path}", timeout=5)
        status = "✅" if response.status_code == expected_status else "❌"
        print(f"{status} {path} - {response.status_code} {description}")
        return response.status_code == expected_status
    except Exception as e:
        print(f"❌ {path} - ERROR: {e}")
        return False

def main():
    print("Testing Garden Planner Routes...")
    print("=" * 50)
    
    # Test main routes
    routes = [
        ("/", 200, "Home page"),
        ("/auth/login", 200, "Login page"),
        ("/auth/register", 200, "Register page"),
        ("/plants/list", 302, "Plants list (redirect to login)"),
        ("/seeds/list", 302, "Seeds list (redirect to login)"),
        ("/property", 302, "Properties (redirect to login)"),
        ("/seasons/list", 302, "Seasons list (redirect to login)"),
        ("/season_plans", 302, "Season plans (redirect to login)"),
        ("/wishlist/plants", 302, "Wishlist (redirect to login)"),
        ("/admin", 302, "Admin (redirect to login)"),
        ("/households", 302, "Households (redirect to login)"),
        ("/profile", 302, "Profile (redirect to login)"),
        ("/settings", 302, "Settings (redirect to login)"),
        ("/notifications/list", 302, "Notifications (redirect to login)"),
        ("/wizard/start", 302, "Wizard (redirect to login)"),
        ("/nonexistent", 404, "404 error page"),
    ]
    
    passed = 0
    total = len(routes)
    
    for path, expected_status, description in routes:
        if test_route(path, expected_status, description):
            passed += 1
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All routes are working correctly!")
        return 0
    else:
        print("⚠️  Some routes have issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
