#!/usr/bin/env python3
"""
Debug script to test wishlist and notification routes specifically
"""
import requests
import random
import string

BASE_URL = "http://127.0.0.1:8080"

def generate_random_username():
    return "testuser_" + str(random.randint(1000000, 9999999))

def test_authenticated_routes():
    session = requests.Session()
    
    # Generate unique username
    username = generate_random_username()
    password = "testpass123"
    
    print(f"Testing authenticated routes with user: {username}")
    
    # Register user
    print("1. Registering user...")
    register_response = session.get(f"{BASE_URL}/auth/register")
    if register_response.status_code != 200:
        print(f"❌ Failed to get register page: {register_response.status_code}")
        return False
    
    register_data = {
        "username": username,
        "password": password,
        "password_confirm": password
    }
    
    register_response = session.post(f"{BASE_URL}/auth/register", data=register_data)
    if register_response.status_code not in [200, 302]:
        print(f"❌ Registration failed: {register_response.status_code}")
        print(f"Response: {register_response.text[:500]}")
        return False
    
    print("✅ Registration successful")
    
    # Login
    print("2. Logging in...")
    login_data = {
        "username": username,
        "password": password
    }
    
    login_response = session.post(f"{BASE_URL}/auth/login", data=login_data)
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        print(f"Response: {login_response.text[:500]}")
        return False
    
    print("✅ Login successful")
    
    # Test wishlist routes
    print("3. Testing wishlist routes...")
    
    # Test plant wishlist
    wishlist_plants_response = session.get(f"{BASE_URL}/wishlist/plants")
    print(f"Wishlist plants response: {wishlist_plants_response.status_code}")
    if wishlist_plants_response.status_code == 200:
        print("✅ Wishlist plants route works")
    else:
        print(f"❌ Wishlist plants route failed: {wishlist_plants_response.status_code}")
        print(f"Response preview: {wishlist_plants_response.text[:500]}")
    
    # Test seed wishlist
    wishlist_seeds_response = session.get(f"{BASE_URL}/wishlist/seeds")
    print(f"Wishlist seeds response: {wishlist_seeds_response.status_code}")
    if wishlist_seeds_response.status_code == 200:
        print("✅ Wishlist seeds route works")
    else:
        print(f"❌ Wishlist seeds route failed: {wishlist_seeds_response.status_code}")
        print(f"Response preview: {wishlist_seeds_response.text[:500]}")
    
    # Test notification API
    print("4. Testing notification API...")
    notifications_response = session.get(f"{BASE_URL}/api/notifications/recent")
    print(f"Notifications API response: {notifications_response.status_code}")
    if notifications_response.status_code == 200:
        print("✅ Notifications API works")
        try:
            data = notifications_response.json()
            print(f"Notifications data: {data}")
        except:
            print("Response is not JSON")
    else:
        print(f"❌ Notifications API failed: {notifications_response.status_code}")
        print(f"Response preview: {notifications_response.text[:500]}")
    
    return True

if __name__ == "__main__":
    success = test_authenticated_routes()
    if not success:
        exit(1)
    print("✅ Authenticated routes test completed")
