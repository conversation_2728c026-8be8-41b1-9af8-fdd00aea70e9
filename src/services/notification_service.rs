use crate::models::{Plant, SeasonPlanPlant, HerbaPlant};
use crate::models::notification::{Notification, NewNotification};
use diesel::SqliteConnection;
use chrono::{NaiveDate, Duration};

/// Service for managing plant care notifications
pub struct NotificationService;

#[derive(Debug, Clone)]
pub struct PlantCareSchedule {
    pub plant_id: i32,
    pub plant_name: String,
    pub watering_frequency: i32, // days
    pub fertilizing_frequency: i32, // days
    pub last_watered: Option<NaiveDate>,
    pub last_fertilized: Option<NaiveDate>,
    pub next_watering: NaiveDate,
    pub next_fertilizing: NaiveDate,
    pub location: String,
}

#[derive(Debug, Clone)]
pub struct NotificationRequest {
    pub plant_id: i32,
    pub notification_type: NotificationType,
    pub due_date: NaiveDate,
    pub message: String,
    pub priority: NotificationPriority,
}

#[derive(Debug, Clone)]
pub enum NotificationType {
    Watering,
    Fertilizing,
    Harvesting,
    Pruning,
    P<PERSON><PERSON>heck,
    GeneralCare,
}

#[derive(Debug, Clone)]
pub enum NotificationPriority {
    Low,
    Medium,
    High,
    Critical,
}

impl NotificationService {
    /// Generate care schedules for all plants in season plans
    pub fn generate_care_schedules(
        conn: &mut SqliteConnection,
        user_id: i32,
    ) -> Result<Vec<PlantCareSchedule>, Box<dyn std::error::Error>> {
        let mut schedules = Vec::new();

        // Get all season plan plants for the user
        let season_plan_plants = SeasonPlanPlant::find_all_for_user(conn, user_id)?;

        for spp in season_plan_plants {
            if let Some(plant) = Plant::find_by_id(conn, spp.plant_id)? {
                let schedule = Self::create_care_schedule(conn, &plant, &spp)?;
                schedules.push(schedule);
            }
        }

        Ok(schedules)
    }

    /// Create care schedule for a specific plant
    fn create_care_schedule(
        conn: &mut SqliteConnection,
        plant: &Plant,
        season_plan_plant: &SeasonPlanPlant,
    ) -> Result<PlantCareSchedule, Box<dyn std::error::Error>> {
        let herba_plant = plant.get_herba_plant(conn)?;

        // Get watering and fertilizing frequencies from herba data or defaults
        let watering_frequency = herba_plant
            .as_ref()
            .and_then(|h| h.get_watering_schedule())
            .unwrap_or(5); // Default: every 5 days

        let fertilizing_frequency = herba_plant
            .as_ref()
            .and_then(|h| h.get_fertilizing_schedule())
            .unwrap_or(21); // Default: every 3 weeks

        let today = chrono::Utc::now().naive_utc().date();

        // For new plants, assume they were last cared for today
        let last_watered = Some(today);
        let last_fertilized = Some(today);

        let next_watering = today + Duration::days(watering_frequency as i64);
        let next_fertilizing = today + Duration::days(fertilizing_frequency as i64);

        let location = format!(
            "Position: ({}, {})",
            season_plan_plant.position_x.unwrap_or(0),
            season_plan_plant.position_y.unwrap_or(0)
        );

        Ok(PlantCareSchedule {
            plant_id: plant.id,
            plant_name: plant.name.clone(),
            watering_frequency,
            fertilizing_frequency,
            last_watered,
            last_fertilized,
            next_watering,
            next_fertilizing,
            location,
        })
    }

    /// Generate notifications for upcoming care tasks
    pub fn generate_care_notifications(
        conn: &mut SqliteConnection,
        user_id: i32,
        days_ahead: i32,
    ) -> Result<Vec<NotificationRequest>, Box<dyn std::error::Error>> {
        let schedules = Self::generate_care_schedules(conn, user_id)?;
        let mut notifications = Vec::new();
        let today = chrono::Utc::now().naive_utc().date();
        let cutoff_date = today + Duration::days(days_ahead as i64);

        for schedule in schedules {
            // Check for watering notifications
            if schedule.next_watering <= cutoff_date {
                let priority = if schedule.next_watering <= today {
                    NotificationPriority::High
                } else if schedule.next_watering <= today + Duration::days(1) {
                    NotificationPriority::Medium
                } else {
                    NotificationPriority::Low
                };

                let message = format!(
                    "Water {} at {} (every {} days)",
                    schedule.plant_name,
                    schedule.location,
                    schedule.watering_frequency
                );

                notifications.push(NotificationRequest {
                    plant_id: schedule.plant_id,
                    notification_type: NotificationType::Watering,
                    due_date: schedule.next_watering,
                    message,
                    priority,
                });
            }

            // Check for fertilizing notifications
            if schedule.next_fertilizing <= cutoff_date {
                let priority = if schedule.next_fertilizing <= today {
                    NotificationPriority::Medium
                } else {
                    NotificationPriority::Low
                };

                let message = format!(
                    "Fertilize {} at {} (every {} days)",
                    schedule.plant_name,
                    schedule.location,
                    schedule.fertilizing_frequency
                );

                notifications.push(NotificationRequest {
                    plant_id: schedule.plant_id,
                    notification_type: NotificationType::Fertilizing,
                    due_date: schedule.next_fertilizing,
                    message,
                    priority,
                });
            }
        }

        Ok(notifications)
    }

    /// Create notifications in the database
    pub fn create_notifications(
        conn: &mut SqliteConnection,
        user_id: i32,
        requests: Vec<NotificationRequest>,
    ) -> Result<Vec<Notification>, Box<dyn std::error::Error>> {
        let mut created_notifications = Vec::new();

        for request in requests {
            let _notification_type = match request.notification_type {
                NotificationType::Watering => "watering",
                NotificationType::Fertilizing => "fertilizing",
                NotificationType::Harvesting => "harvesting",
                NotificationType::Pruning => "pruning",
                NotificationType::PestCheck => "pest_check",
                NotificationType::GeneralCare => "general_care",
            };

            let _priority = match request.priority {
                NotificationPriority::Low => "low",
                NotificationPriority::Medium => "medium",
                NotificationPriority::High => "high",
                NotificationPriority::Critical => "critical",
            };

            let scheduled_time = request.due_date.and_hms_opt(9, 0, 0).unwrap(); // Schedule for 9 AM

            let new_notification = NewNotification {
                user_id,
                plant_id: request.plant_id,
                message: &request.message,
                scheduled_time,
                sent: false,
            };

            let notification = Notification::create(conn, &new_notification)?;
            created_notifications.push(notification);
        }

        Ok(created_notifications)
    }

    /// Get overdue notifications for a user
    pub fn get_overdue_notifications(
        conn: &mut SqliteConnection,
        user_id: i32,
    ) -> Result<Vec<Notification>, Box<dyn std::error::Error>> {
        let today = chrono::Utc::now().naive_utc().date();
        Ok(Notification::find_overdue_for_user(conn, user_id, today)?)
    }

    /// Get upcoming notifications for a user
    pub fn get_upcoming_notifications(
        conn: &mut SqliteConnection,
        user_id: i32,
        days_ahead: i32,
    ) -> Result<Vec<Notification>, Box<dyn std::error::Error>> {
        let today = chrono::Utc::now().naive_utc().date();
        let end_date = today + Duration::days(days_ahead as i64);
        Ok(Notification::find_upcoming_for_user(conn, user_id, today, end_date)?)
    }

    /// Mark a notification as completed and update care schedule
    pub fn complete_care_task(
        conn: &mut SqliteConnection,
        notification_id: i32,
        user_id: i32,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Mark notification as read
        Notification::mark_as_read(conn, notification_id, user_id)?;

        // Get the notification to understand what task was completed
        if let Some(notification) = Notification::find_by_id(conn, notification_id)? {
            let plant_id = notification.plant_id;
            // Extract notification type from message (simplified approach)
            let notification_type = if notification.message.to_lowercase().contains("water") {
                "watering"
            } else if notification.message.to_lowercase().contains("fertiliz") {
                "fertilizing"
            } else {
                "general_care"
            };

            // Update the care schedule based on notification type
            Self::update_care_schedule(conn, plant_id, notification_type)?;

            // Generate next notification for this care type
            Self::schedule_next_care_notification(conn, user_id, plant_id, notification_type)?;
        }

        Ok(())
    }

    /// Update care schedule when a task is completed
    fn update_care_schedule(
        _conn: &mut SqliteConnection,
        plant_id: i32,
        care_type: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // This would update a care_schedule table if we had one
        // For now, we'll just log the completion
        log::info!("Care task '{}' completed for plant {}", care_type, plant_id);
        Ok(())
    }

    /// Schedule the next notification for a care type
    fn schedule_next_care_notification(
        conn: &mut SqliteConnection,
        user_id: i32,
        plant_id: i32,
        care_type: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(plant) = Plant::find_by_id(conn, plant_id)? {
            let herba_plant = plant.get_herba_plant(conn)?;

            let frequency = match care_type {
                "watering" => herba_plant
                    .as_ref()
                    .and_then(|h| h.get_watering_schedule())
                    .unwrap_or(5),
                "fertilizing" => herba_plant
                    .as_ref()
                    .and_then(|h| h.get_fertilizing_schedule())
                    .unwrap_or(21),
                _ => return Ok(()), // Don't schedule for other types
            };

            let today = chrono::Utc::now().naive_utc().date();
            let next_due = today + Duration::days(frequency as i64);

            let message = format!(
                "{} {} (every {} days)",
                care_type.to_uppercase(),
                plant.name,
                frequency
            );

            let scheduled_time = next_due.and_hms_opt(9, 0, 0).unwrap(); // Schedule for 9 AM

            let new_notification = NewNotification {
                user_id,
                plant_id,
                message: &message,
                scheduled_time,
                sent: false,
            };

            Notification::create(conn, &new_notification)?;
        }

        Ok(())
    }

    /// Generate seasonal care recommendations
    pub fn generate_seasonal_recommendations(
        conn: &mut SqliteConnection,
        user_id: i32,
        season: &str,
    ) -> Result<Vec<NotificationRequest>, Box<dyn std::error::Error>> {
        let mut recommendations = Vec::new();
        let today = chrono::Utc::now().naive_utc().date();

        // Get all user's plants
        let season_plan_plants = SeasonPlanPlant::find_all_for_user(conn, user_id)?;

        for spp in season_plan_plants {
            if let Some(plant) = Plant::find_by_id(conn, spp.plant_id)? {
                let herba_plant = plant.get_herba_plant(conn)?;

                // Generate season-specific recommendations
                let seasonal_tasks = Self::get_seasonal_tasks(&plant, herba_plant.as_ref(), season);

                for task in seasonal_tasks {
                    recommendations.push(NotificationRequest {
                        plant_id: plant.id,
                        notification_type: task.0,
                        due_date: today + Duration::days(task.1),
                        message: format!("{} for {}", task.2, plant.name),
                        priority: NotificationPriority::Medium,
                    });
                }
            }
        }

        Ok(recommendations)
    }

    /// Get seasonal care tasks for a plant
    fn get_seasonal_tasks(
        _plant: &Plant,
        herba_plant: Option<&HerbaPlant>,
        season: &str,
    ) -> Vec<(NotificationType, i64, String)> {
        let mut tasks = Vec::new();

        match season.to_lowercase().as_str() {
            "spring" => {
                tasks.push((NotificationType::Fertilizing, 7, "Spring fertilizing".to_string()));
                tasks.push((NotificationType::PestCheck, 14, "Spring pest inspection".to_string()));
                if let Some(herba) = herba_plant {
                    if herba.growth_habit.as_deref() == Some("perennial") {
                        tasks.push((NotificationType::Pruning, 3, "Spring pruning".to_string()));
                    }
                }
            }
            "summer" => {
                tasks.push((NotificationType::Watering, 1, "Increase summer watering".to_string()));
                tasks.push((NotificationType::Harvesting, 30, "Check for harvest readiness".to_string()));
            }
            "autumn" | "fall" => {
                tasks.push((NotificationType::Harvesting, 7, "Final harvest".to_string()));
                tasks.push((NotificationType::GeneralCare, 14, "Prepare for winter".to_string()));
            }
            "winter" => {
                tasks.push((NotificationType::GeneralCare, 30, "Winter protection check".to_string()));
            }
            _ => {}
        }

        tasks
    }
}
