use reqwest;
use serde::{Deserialize, Serialize};


#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct HerbaPlantInfo {
    pub name: String,
    pub scientific_name: Option<String>,
    pub common_names: Vec<String>,
    pub description: Option<String>,
    pub plant_type: Option<String>,
    pub watering_needs: Option<String>,
    pub light_requirements: Option<String>,
    pub soil_type: Option<String>,
    pub hardiness_zone: Option<String>,
    pub growth_habit: Option<String>,
    pub mature_size: Option<String>,
    pub bloom_time: Option<String>,
    pub flower_color: Option<String>,
    pub foliage_color: Option<String>,
    pub special_features: Vec<String>,
    pub care_instructions: Option<String>,
    pub propagation_methods: Vec<String>,
    pub companion_plants: Vec<String>,
    pub pests_diseases: Vec<String>,
    pub uses: Vec<String>,
}

pub struct HerbaScraper {
    client: reqwest::Client,
    base_urls: Vec<String>,
}

impl HerbaScraper {
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::new(),
            base_urls: vec![
                "https://www.missouribotanicalgarden.org".to_string(),
                "https://plants.usda.gov".to_string(),
                "https://www.rhs.org.uk".to_string(),
                "https://www.gardeningknowhow.com".to_string(),
            ],
        }
    }

    /// Search for plant information by name
    pub async fn search_plant(&self, plant_name: &str) -> Result<Vec<HerbaPlantInfo>, Box<dyn std::error::Error>> {
        println!("Searching for plant: {}", plant_name);

        let mut results = Vec::new();

        // Try different search strategies
        if let Ok(info) = self.search_missouri_botanical(&plant_name).await {
            results.push(info);
        }

        if let Ok(info) = self.search_usda_plants(&plant_name).await {
            results.push(info);
        }

        if let Ok(info) = self.search_rhs(&plant_name).await {
            results.push(info);
        }

        Ok(results)
    }

    /// Search Missouri Botanical Garden database
    async fn search_missouri_botanical(&self, plant_name: &str) -> Result<HerbaPlantInfo, Box<dyn std::error::Error>> {
        // This is a simplified example - in reality you'd need to parse HTML or use APIs
        let search_url = format!("https://www.missouribotanicalgarden.org/PlantFinder/PlantFinderSearch.aspx?searchtext={}",
                                urlencoding::encode(plant_name));

        println!("Searching Missouri Botanical Garden: {}", search_url);

        // For now, return mock data - in a real implementation you'd parse the response
        Ok(HerbaPlantInfo {
            name: plant_name.to_string(),
            scientific_name: Some(format!("{} species", plant_name)),
            common_names: vec![plant_name.to_string()],
            description: Some("Plant information from Missouri Botanical Garden".to_string()),
            plant_type: Some("Perennial".to_string()),
            watering_needs: Some("Moderate".to_string()),
            light_requirements: Some("Full sun to partial shade".to_string()),
            soil_type: Some("Well-drained".to_string()),
            hardiness_zone: Some("5-9".to_string()),
            growth_habit: Some("Upright".to_string()),
            mature_size: Some("2-3 feet".to_string()),
            bloom_time: Some("Summer".to_string()),
            flower_color: Some("Various".to_string()),
            foliage_color: Some("Green".to_string()),
            special_features: vec!["Attracts butterflies".to_string(), "Deer resistant".to_string()],
            care_instructions: Some("Water regularly, fertilize monthly during growing season".to_string()),
            propagation_methods: vec!["Seeds".to_string(), "Division".to_string()],
            companion_plants: vec!["Lavender".to_string(), "Rosemary".to_string()],
            pests_diseases: vec!["Aphids".to_string(), "Powdery mildew".to_string()],
            uses: vec!["Ornamental".to_string(), "Medicinal".to_string()],
        })
    }

    /// Search USDA Plants database
    async fn search_usda_plants(&self, plant_name: &str) -> Result<HerbaPlantInfo, Box<dyn std::error::Error>> {
        let search_url = format!("https://plants.usda.gov/home/<USER>",
                                urlencoding::encode(plant_name));

        println!("Searching USDA Plants: {}", search_url);

        // Mock data for now
        Ok(HerbaPlantInfo {
            name: plant_name.to_string(),
            scientific_name: Some(format!("{} officinalis", plant_name)),
            common_names: vec![plant_name.to_string(), format!("Common {}", plant_name)],
            description: Some("Plant information from USDA Plants database".to_string()),
            plant_type: Some("Herb".to_string()),
            watering_needs: Some("Low to moderate".to_string()),
            light_requirements: Some("Full sun".to_string()),
            soil_type: Some("Sandy, well-drained".to_string()),
            hardiness_zone: Some("4-8".to_string()),
            growth_habit: Some("Bushy".to_string()),
            mature_size: Some("1-2 feet".to_string()),
            bloom_time: Some("Late spring to early summer".to_string()),
            flower_color: Some("Purple, white".to_string()),
            foliage_color: Some("Gray-green".to_string()),
            special_features: vec!["Aromatic".to_string(), "Drought tolerant".to_string()],
            care_instructions: Some("Minimal care required, prune after flowering".to_string()),
            propagation_methods: vec!["Cuttings".to_string(), "Seeds".to_string()],
            companion_plants: vec!["Thyme".to_string(), "Oregano".to_string()],
            pests_diseases: vec!["Root rot".to_string(), "Spider mites".to_string()],
            uses: vec!["Culinary".to_string(), "Aromatherapy".to_string(), "Landscaping".to_string()],
        })
    }

    /// Search RHS (Royal Horticultural Society) database
    async fn search_rhs(&self, plant_name: &str) -> Result<HerbaPlantInfo, Box<dyn std::error::Error>> {
        let search_url = format!("https://www.rhs.org.uk/plants/search-results?query={}",
                                urlencoding::encode(plant_name));

        println!("Searching RHS: {}", search_url);

        // Mock data for now
        Ok(HerbaPlantInfo {
            name: plant_name.to_string(),
            scientific_name: Some(format!("{} hortensis", plant_name)),
            common_names: vec![plant_name.to_string(), format!("Garden {}", plant_name)],
            description: Some("Plant information from RHS database".to_string()),
            plant_type: Some("Annual".to_string()),
            watering_needs: Some("Regular".to_string()),
            light_requirements: Some("Partial shade".to_string()),
            soil_type: Some("Moist, fertile".to_string()),
            hardiness_zone: Some("Annual".to_string()),
            growth_habit: Some("Compact".to_string()),
            mature_size: Some("6-12 inches".to_string()),
            bloom_time: Some("All season".to_string()),
            flower_color: Some("White, pink".to_string()),
            foliage_color: Some("Bright green".to_string()),
            special_features: vec!["Edible".to_string(), "Fast growing".to_string()],
            care_instructions: Some("Keep soil moist, harvest regularly".to_string()),
            propagation_methods: vec!["Seeds".to_string()],
            companion_plants: vec!["Basil".to_string(), "Parsley".to_string()],
            pests_diseases: vec!["Slugs".to_string(), "Downy mildew".to_string()],
            uses: vec!["Culinary".to_string(), "Garnish".to_string()],
        })
    }

    /// Get comprehensive plant information by combining multiple sources
    pub async fn get_comprehensive_info(&self, plant_name: &str) -> Result<HerbaPlantInfo, Box<dyn std::error::Error>> {
        let results = self.search_plant(plant_name).await?;

        if results.is_empty() {
            return Err("No plant information found".into());
        }

        // Combine information from multiple sources
        let mut combined = results[0].clone();

        for result in results.iter().skip(1) {
            // Merge common names
            for name in &result.common_names {
                if !combined.common_names.contains(name) {
                    combined.common_names.push(name.clone());
                }
            }

            // Merge special features
            for feature in &result.special_features {
                if !combined.special_features.contains(feature) {
                    combined.special_features.push(feature.clone());
                }
            }

            // Use more detailed description if available
            if result.description.is_some() && combined.description.is_none() {
                combined.description = result.description.clone();
            }

            // Merge other fields as needed
            if combined.scientific_name.is_none() && result.scientific_name.is_some() {
                combined.scientific_name = result.scientific_name.clone();
            }
        }

        Ok(combined)
    }
}

/// Public function to scrape plant information
pub async fn scrape_plant_info(plant_name: &str) -> Result<HerbaPlantInfo, Box<dyn std::error::Error>> {
    let scraper = HerbaScraper::new();
    scraper.get_comprehensive_info(plant_name).await
}
