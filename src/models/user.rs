use diesel::prelude::*;
use serde::{Deserialize, Serialize};

use crate::schema::users;

#[derive(Debug, Clone, Queryable, Identifiable, Deserialize, AsChangeset, Selectable)]
#[diesel(table_name = users)]
pub struct User {
    pub id: i32,
    pub username: String,
    pub password_hash: String,
    pub role: String,
}

#[derive(Debug, <PERSON>lone, Serialize)]
pub struct SafeUser {
    pub id: i32,
    pub username: String,
    pub role: String,
}

impl From<User> for SafeUser {
    fn from(user: User) -> Self {
        SafeUser {
            id: user.id,
            username: user.username,
            role: user.role,
        }
    }
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = users)]
pub struct NewUser<'a> {
    pub username: &'a str,
    pub password_hash: &'a str,
    pub role: &'a str,
}

#[derive(Deserialize)]
pub struct UpdateUserRoleForm {
    pub _user_id: i32,
    pub _role: String,
}
