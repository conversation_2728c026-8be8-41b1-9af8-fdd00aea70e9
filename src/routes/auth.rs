use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;
use log::error;

use crate::models::user::{NewUser, User};
use crate::models::household::Household;

use crate::schema::users::dsl::*;
use crate::schema::{users, households, user_households};
use crate::utils::hashing::{hash_password, verify_password};
use crate::utils::templates::{render_template_with_context};

use crate::utils::validation::{validate_username, validate_password};
use crate::DbPool;

#[derive(Deserialize)]
pub struct RegisterForm {
    pub username: String,
    pub password: String,
    pub password_confirm: String,
}

#[derive(Deserialize)]
pub struct LoginForm {
    pub username: String,
    pub password: String,
}

pub async fn register_form(session: Session) -> Result<HttpResponse, actix_web::Error> {
    let mut ctx = tera::Context::new();
    render_template_with_context("auth/register.html", &mut ctx, &session)
}

pub async fn register(
    form: web::Form<RegisterForm>,
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Validate username
    if let Err(e) = validate_username(&form.username) {
        return Ok(HttpResponse::BadRequest().content_type("text/html").body(
            format!("Error: {}", e.to_string())
        ));
    }

    // Validate password
    if let Err(e) = validate_password(&form.password) {
        return Ok(HttpResponse::BadRequest().content_type("text/html").body(
            format!("Error: {}", e.to_string())
        ));
    }

    // Validate password confirmation
    if form.password != form.password_confirm {
        return Ok(HttpResponse::BadRequest().content_type("text/html").body(
            "Error: Passwords do not match"
        ));
    }

    let existing_user = users::table
        .filter(users::username.eq(&form.username))
        .first::<User>(&mut conn)
        .optional()
        .map_err(|e| {
            error!("Database error checking existing user: {}", e);
            actix_web::error::ErrorInternalServerError("Database error")
        })?;

    if existing_user.is_some() {
        return Ok(HttpResponse::BadRequest().content_type("text/html").body(
            "Error: Username already exists. Please choose another one."
        ));
    }

    // Hash the password
    let hashed_password = match hash_password(&form.password) {
        Ok(hash) => hash,
        Err(e) => {
            error!("Failed to hash password: {}", e);
            return Ok(HttpResponse::InternalServerError().content_type("text/html").body(
                "Error: An error occurred during registration"
            ));
        }
    };

    // Check if this is the first user (should be superadmin)
    let user_count: i64 = users::table.count().get_result(&mut conn).unwrap_or(0);
    let user_role = if user_count == 0 {
        println!("Creating first user as superadmin: {}", form.username);
        "superadmin"
    } else {
        "user"
    };

    let new_user = NewUser {
        username: &form.username,
        password_hash: &hashed_password,
        role: user_role,
    };

    // Insert the new user
    diesel::insert_into(users)
        .values(&new_user)
        .execute(&mut conn)
        .map_err(|e| {
            error!("Database error inserting new user: {}", e);
            actix_web::error::ErrorInternalServerError("Database error")
        })?;

    // Retrieve the inserted user to get the ID
    let inserted_user = users
        .filter(username.eq(&form.username))
        .first::<User>(&mut conn)
        .map_err(|e| {
            error!("Database error retrieving inserted user: {}", e);
            actix_web::error::ErrorInternalServerError("Database error")
        })?;

    // Store user info in sessionllk
    session.insert("user_id", inserted_user.id)?;
    session.insert("username", inserted_user.username.clone())?;
    session.insert("role", inserted_user.role.clone())?;

    // Redirect to the household creation wizard
    Ok(HttpResponse::Found()
        .append_header(("Location", "/wizard/household"))
        .finish())
}

pub async fn login_form(session: Session) -> Result<HttpResponse, actix_web::Error> {
    let mut ctx = tera::Context::new();
    render_template_with_context("auth/login.html", &mut ctx, &session)
}

pub async fn login(
    form: web::Form<LoginForm>,
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let user = users
        .filter(username.eq(&form.username))
        .first::<User>(&mut conn)
        .optional()
        .map_err(|e| {
            error!("Database error loading user: {}", e);
            actix_web::error::ErrorInternalServerError("Database error")
        })?;

    if let Some(user) = user {
        if verify_password(&form.password, &user.password_hash) {
            println!("Login successful for user: {} (id: {})", user.username, user.id);
            session.insert("user_id", user.id)?;
            session.insert("username", user.username.clone())?;
            session.insert("role", user.role.clone())?;

            // Set default household if user has any
            let user_household = user_households::table
                .filter(user_households::user_id.eq(user.id))
                .inner_join(households::table)
                .select((households::all_columns, user_households::role))
                .first::<(Household, String)>(&mut conn)
                .optional()
                .map_err(|e| {
                    error!("Database error loading user household: {}", e);
                    actix_web::error::ErrorInternalServerError("Database error")
                })?;

            if let Some((household, _role)) = user_household {
                session.insert("current_household_id", household.id)?;
                session.insert("current_household_name", household.name)?;
            }

            // Verify session data was set
            let verify_user_id = session.get::<i32>("user_id").unwrap_or(None);
            let verify_username = session.get::<String>("username").unwrap_or(None);
            let verify_role = session.get::<String>("role").unwrap_or(None);
            println!("Session data verification - user_id: {:?}, username: {:?}, role: {:?}",
                     verify_user_id, verify_username, verify_role);

            println!("Session data set - user_id: {}, username: {}, role: {}", user.id, user.username, user.role);
            return Ok(HttpResponse::Found()
                .append_header(("Location", "/"))
                .finish());
        }
    }

    // Handle invalid login - render form with error
    let mut ctx = tera::Context::new();
    ctx.insert("error", "Invalid username or password");
    ctx.insert("username", &form.username);
    render_template_with_context("auth/login.html", &mut ctx, &session)
}

pub async fn logout(session: Session) -> Result<HttpResponse, actix_web::Error> {
    println!("Logout called - purging session");
    session.purge();
    println!("Session purged, redirecting to login");
    Ok(HttpResponse::Found()
        .append_header(("Location", "/auth/login"))
        .finish())
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/auth")
            .route("/register", web::get().to(register_form))
            .route("/register", web::post().to(register))
            .route("/login", web::get().to(login_form))
            .route("/login", web::post().to(login))
            .route("/logout", web::get().to(logout)),
    );
}
