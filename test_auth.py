#!/usr/bin/env python3
"""
Test authentication and redirects
"""
import requests
import sys

BASE_URL = "http://127.0.0.1:8080"

def test_auth_redirect(path):
    """Test if a protected route redirects to login"""
    try:
        response = requests.get(f"{BASE_URL}{path}", allow_redirects=False, timeout=5)
        print(f"Testing {path}:")
        print(f"  Status: {response.status_code}")
        print(f"  Headers: {dict(response.headers)}")
        if 'Location' in response.headers:
            print(f"  Redirect to: {response.headers['Location']}")
        print(f"  Content preview: {response.text[:200]}...")
        print("-" * 50)
        return response.status_code, response.headers.get('Location', '')
    except Exception as e:
        print(f"Error testing {path}: {e}")
        return None, None

def main():
    print("Testing authentication redirects...")
    print("=" * 50)
    
    # Test protected routes
    routes = [
        "/plants/list",
        "/seeds/list", 
        "/property",
        "/admin",
        "/profile",
        "/settings"
    ]
    
    for route in routes:
        test_auth_redirect(route)

if __name__ == "__main__":
    main()
