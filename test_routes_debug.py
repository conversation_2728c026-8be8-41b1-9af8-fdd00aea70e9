#!/usr/bin/env python3
"""
Debug script to test which routes are actually working
"""
import requests

BASE_URL = "http://127.0.0.1:8080"

def test_routes():
    session = requests.Session()
    
    routes_to_test = [
        "/",
        "/auth/login",
        "/auth/register", 
        "/plants/list",
        "/seeds/list",
        "/property",
        "/seasons/list",
        "/season_plans",
        "/households",
        "/profile",
        "/settings",
        "/notifications/list",
        "/wishlist/plants",
        "/wishlist/seeds",
        "/api/notifications/recent",
        "/admin",
        "/wizard/start",
        "/test",
        "/test-template"
    ]
    
    print("Testing routes without authentication:")
    print("=" * 50)
    
    for route in routes_to_test:
        try:
            response = session.get(f"{BASE_URL}{route}")
            status = response.status_code
            
            if status == 200:
                print(f"✅ {route} - {status} (OK)")
            elif status == 302:
                location = response.headers.get('location', 'Unknown')
                print(f"🔄 {route} - {status} (Redirect to {location})")
            elif status == 404:
                print(f"❌ {route} - {status} (Not Found)")
            elif status == 401:
                print(f"🔒 {route} - {status} (Unauthorized)")
            elif status == 500:
                print(f"💥 {route} - {status} (Server Error)")
            else:
                print(f"⚠️  {route} - {status} (Other)")
                
        except Exception as e:
            print(f"💥 {route} - Error: {e}")
    
    print("\n" + "=" * 50)
    print("Route test completed")

if __name__ == "__main__":
    test_routes()
