{% extends "base.html" %}
{% block title %}{{ household.name }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">{{ household.name }}</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Household Management & Collaboration</p>
        </div>
        <div class="flex space-x-3">
            {% if is_owner or user_role == 'admin' %}
            <button onclick="toggleShareModal()" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Invite Members
            </button>
            <a href="/households/{{ household.id }}/edit" class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Settings
            </a>
            {% endif %}
            <a href="/households" class="bg-sage-200 hover:bg-sage-300 dark:bg-sage-700 dark:hover:bg-sage-600 text-sage-800 dark:text-sage-200 px-4 py-2 rounded-material transition-colors">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Households
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Household Info -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-sage-800 rounded-material shadow-material-2 p-6 mb-6 border border-sage-200 dark:border-sage-700">
                <h2 class="text-xl font-semibold mb-4 text-sage-900 dark:text-sage-100">Household Information</h2>
                <div class="grid grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2">Name</label>
                        <p class="text-sage-900 dark:text-sage-100 text-lg">{{ household.name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2">Your Role</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                   {% if user_role == 'admin' %}bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200{% else %}bg-secondary-100 dark:bg-secondary-900 text-secondary-800 dark:text-secondary-200{% endif %}">
                            {{ user_role | title }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Members List -->
            <div class="bg-white dark:bg-sage-800 rounded-material shadow-material-2 p-6 border border-sage-200 dark:border-sage-700">
                <h2 class="text-xl font-semibold mb-4 text-sage-900 dark:text-sage-100">Members ({{ members | length }})</h2>
                <div class="space-y-3">
                    {% for member in members %}
                    <div class="flex items-center justify-between p-4 bg-sage-50 dark:bg-sage-700 rounded-material border border-sage-200 dark:border-sage-600 hover:shadow-material-1 transition-shadow">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-lg shadow-material-1">
                                {% if member.username and member.username|length > 0 %}{{ member.username | truncate(length=1, end="") | upper }}{% else %}?{% endif %}
                            </div>
                            <div>
                                <p class="font-medium text-sage-900 dark:text-sage-100">{{ member.username }}</p>
                                <p class="text-sm text-sage-600 dark:text-sage-400">
                                    {% if member.id == household.owner_id %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-tertiary-100 dark:bg-tertiary-900 text-tertiary-800 dark:text-tertiary-200">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.636-.525-3.15-1.414-4.243a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            Owner
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary-100 dark:bg-secondary-900 text-secondary-800 dark:text-secondary-200">
                                            {{ member.role | default(value="Member") | title }}
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        {% if is_owner and member.id != household.owner_id %}
                        <div class="flex space-x-2">
                            <button class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium px-3 py-1 rounded-material hover:bg-primary-50 dark:hover:bg-primary-900 transition-colors">Edit Role</button>
                            <button class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium px-3 py-1 rounded-material hover:bg-red-50 dark:hover:bg-red-900 transition-colors">Remove</button>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="space-y-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="/property" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"></path>
                        </svg>
                        View Properties
                    </a>
                    <a href="/season_plans" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Season Plans
                    </a>
                    <a href="/plants/list" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                        Plant Database
                    </a>
                    <a href="/seeds/list" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Seed Collection
                    </a>
                </div>
            </div>

            <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                            Household Features
                        </h3>
                        <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                            <ul class="list-disc list-inside">
                                <li>Shared property management</li>
                                <li>Collaborative season planning</li>
                                <li>Plant and seed databases</li>
                                <li>Member role management</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Share Modal -->
<div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-sage-800 rounded-material shadow-material-5 p-6 w-full max-w-md mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100">Invite Member</h3>
            <button onclick="toggleShareModal()" class="text-sage-500 hover:text-sage-700 dark:text-sage-400 dark:hover:text-sage-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="post" action="/households/{{ household.id }}/share" class="space-y-4">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div>
                <label for="username" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2">Username</label>
                <input type="text" id="username" name="username" required
                       class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-material focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-sage-700 dark:text-sage-100"
                       placeholder="Enter username to invite">
            </div>

            <div>
                <label for="role" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2">Permission Level</label>
                <select id="role" name="role" required
                        class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-material focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-sage-700 dark:text-sage-100">
                    <option value="view">View Only - Read-only access for showing off/bragging</option>
                    <option value="comment">Comment & Notifications - Care-taking access with notification rights</option>
                    <option value="manage">Manage - Full management rights equivalent to creator</option>
                </select>
            </div>

            <div class="flex items-center justify-end space-x-3 pt-4">
                <button type="button" onclick="toggleShareModal()"
                        class="px-4 py-2 border border-sage-300 dark:border-sage-600 text-sage-700 dark:text-sage-300 rounded-material hover:bg-sage-50 dark:hover:bg-sage-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200">
                    Send Invitation
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function toggleShareModal() {
    const modal = document.getElementById('shareModal');
    modal.classList.toggle('hidden');
}
</script>
{% endblock %}
