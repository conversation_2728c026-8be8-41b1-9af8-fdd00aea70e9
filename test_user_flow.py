#!/usr/bin/env python3
"""
Test complete user flow: registration, login, navigation
"""
import requests
import sys
import time

BASE_URL = "http://127.0.0.1:8080"

class TestSession:
    def __init__(self):
        self.session = requests.Session()
        
    def test_registration(self):
        """Test user registration"""
        print("Testing user registration...")
        
        # Get registration form
        response = self.session.get(f"{BASE_URL}/auth/register")
        if response.status_code != 200:
            print(f"❌ Failed to get registration form: {response.status_code}")
            return False
            
        # Register new user
        test_username = f"testuser_{int(time.time())}"
        registration_data = {
            'username': test_username,
            'password': 'testpassword123',
            'password_confirm': 'testpassword123'
        }
        
        response = self.session.post(f"{BASE_URL}/auth/register", data=registration_data)
        if response.status_code in [200, 302]:
            print(f"✅ Registration successful for {test_username}")
            self.username = test_username
            return True
        else:
            print(f"❌ Registration failed: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
    
    def test_login(self):
        """Test user login"""
        print("Testing user login...")
        
        # Get login form
        response = self.session.get(f"{BASE_URL}/auth/login")
        if response.status_code != 200:
            print(f"❌ Failed to get login form: {response.status_code}")
            return False
            
        # Login
        login_data = {
            'username': self.username,
            'password': 'testpassword123'
        }
        
        response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
        if response.status_code in [200, 302]:
            print("✅ Login successful")
            return True
        else:
            print(f"❌ Login failed: {response.status_code}")
            return False
    
    def test_authenticated_routes(self):
        """Test access to authenticated routes"""
        print("Testing authenticated routes...")
        
        routes = [
            ("/", "Dashboard"),
            ("/plants/list", "Plants list"),
            ("/seeds/list", "Seeds list"),
            ("/property", "Properties"),
            ("/households", "Households"),
            ("/profile", "Profile"),
            ("/settings", "Settings"),
            ("/notifications/list", "Notifications"),
        ]
        
        success_count = 0
        for route, description in routes:
            response = self.session.get(f"{BASE_URL}{route}")
            if response.status_code == 200:
                print(f"✅ {description} accessible")
                success_count += 1
            else:
                print(f"❌ {description} failed: {response.status_code}")
        
        return success_count == len(routes)
    
    def test_logout(self):
        """Test user logout"""
        print("Testing logout...")
        
        response = self.session.get(f"{BASE_URL}/auth/logout")
        if response.status_code in [200, 302]:
            print("✅ Logout successful")
            return True
        else:
            print(f"❌ Logout failed: {response.status_code}")
            return False

def main():
    print("Garden Planner - Complete User Flow Test")
    print("=" * 50)
    
    test_session = TestSession()
    
    # Test complete flow
    tests = [
        ("Registration", test_session.test_registration),
        ("Login", test_session.test_login),
        ("Authenticated Routes", test_session.test_authenticated_routes),
        ("Logout", test_session.test_logout),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed - stopping tests")
            break
    
    print("\n" + "=" * 50)
    print(f"Results: {passed}/{total} test phases passed")
    
    if passed == total:
        print("🎉 Complete user flow working correctly!")
        return 0
    else:
        print("⚠️  Some functionality has issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
