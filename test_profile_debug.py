#!/usr/bin/env python3
"""
Debug script to test profile route specifically
"""
import requests
import random
import string

BASE_URL = "http://127.0.0.1:8080"

def generate_random_username():
    return "testuser_" + str(random.randint(1000000, 9999999))

def test_profile_route():
    session = requests.Session()
    
    # Generate unique username
    username = generate_random_username()
    password = "testpass123"
    
    print(f"Testing profile route with user: {username}")
    
    # Register user
    print("1. Registering user...")
    register_response = session.get(f"{BASE_URL}/auth/register")
    if register_response.status_code != 200:
        print(f"❌ Failed to get register page: {register_response.status_code}")
        return False
    
    register_data = {
        "username": username,
        "password": password,
        "password_confirm": password
    }
    
    register_response = session.post(f"{BASE_URL}/auth/register", data=register_data)
    if register_response.status_code not in [200, 302]:
        print(f"❌ Registration failed: {register_response.status_code}")
        print(f"Response: {register_response.text[:500]}")
        return False
    
    print("✅ Registration successful")
    
    # Login
    print("2. Logging in...")
    login_data = {
        "username": username,
        "password": password
    }
    
    login_response = session.post(f"{BASE_URL}/auth/login", data=login_data)
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        print(f"Response: {login_response.text[:500]}")
        return False
    
    print("✅ Login successful")
    
    # Test profile route
    print("3. Testing profile route...")
    profile_response = session.get(f"{BASE_URL}/profile")
    
    print(f"Profile response status: {profile_response.status_code}")
    print(f"Profile response headers: {dict(profile_response.headers)}")
    
    if profile_response.status_code == 500:
        print("❌ Profile route returned 500 error")
        print("Response content:")
        print(profile_response.text[:1000])
        return False
    elif profile_response.status_code == 200:
        print("✅ Profile route works correctly")
        # Check if it contains expected content
        if "My Profile" in profile_response.text:
            print("✅ Profile page contains expected content")
            return True
        else:
            print("⚠️  Profile page missing expected content")
            print("Response preview:")
            print(profile_response.text[:500])
            return False
    else:
        print(f"❌ Unexpected status code: {profile_response.status_code}")
        print(f"Response: {profile_response.text[:500]}")
        return False

if __name__ == "__main__":
    success = test_profile_route()
    if not success:
        exit(1)
    print("✅ Profile route test completed successfully")
